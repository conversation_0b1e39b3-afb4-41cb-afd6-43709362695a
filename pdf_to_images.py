import fitz  # PyMuPDF
import os
from pathlib import Path

def pdf_to_images(pdf_path, output_dir=None, dpi=300, image_format='png'):
    """
    将PDF文件转换为图片
    
    Args:
        pdf_path (str): PDF文件路径
        output_dir (str): 输出目录，默认为PDF文件同目录
        dpi (int): 图片分辨率，默认300
        image_format (str): 图片格式，支持'png', 'jpg', 'jpeg'
    
    Returns:
        list: 生成的图片文件路径列表
    """
    pdf_path = Path(pdf_path)
    
    if not pdf_path.exists():
        raise FileNotFoundError(f"PDF文件不存在: {pdf_path}")
    
    # 设置输出目录
    if output_dir is None:
        output_dir = pdf_path.parent / f"{pdf_path.stem}_images"
    else:
        output_dir = Path(output_dir)
    
    output_dir.mkdir(exist_ok=True)
    
    # 打开PDF文件
    pdf_document = fitz.open(pdf_path)
    image_paths = []
    
    try:
        for page_num in range(pdf_document.page_count):
            page = pdf_document[page_num]
            
            # 设置缩放比例以控制分辨率
            zoom = dpi / 72  # 72是PDF的默认DPI
            mat = fitz.Matrix(zoom, zoom)
            
            # 渲染页面为图片
            pix = page.get_pixmap(matrix=mat)
            
            # 生成文件名
            image_filename = f"page_{page_num + 1:03d}.{image_format.lower()}"
            image_path = output_dir / image_filename
            
            # 保存图片
            if image_format.lower() in ['jpg', 'jpeg']:
                pix.save(str(image_path), output="jpeg")
            else:
                pix.save(str(image_path))
            
            image_paths.append(str(image_path))
            print(f"已转换第 {page_num + 1} 页: {image_path}")
    
    finally:
        pdf_document.close()
    
    print(f"转换完成！共生成 {len(image_paths)} 张图片")
    return image_paths

def main():
    # 使用示例
    pdf_file = "管家行产品介绍（弱电工程行业）.pdf"
    
    try:
        images = pdf_to_images(
            pdf_path=pdf_file,
            dpi=300,  # 高质量
            image_format='png'
        )
        
        print("生成的图片文件:")
        for img in images:
            print(f"  {img}")
            
    except Exception as e:
        print(f"转换失败: {e}")

if __name__ == "__main__":
    main()