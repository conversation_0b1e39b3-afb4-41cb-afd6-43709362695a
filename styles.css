/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏样式 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e5e5e5;
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-img {
    height: 40px;
    width: auto;
}

.logo-text {
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.nav-menu {
    display: flex;
    gap: 32px;
}

.nav-link {
    text-decoration: none;
    color: #666;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #06a17e;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #06a17e;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* 按钮样式 */
.btn-primary {
    background: #06a17e;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary:hover {
    background: #058a6e;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(6, 161, 126, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #06a17e;
    border: 2px solid #06a17e;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-secondary:hover {
    background: #06a17e;
    color: white;
    transform: translateY(-2px);
}

.btn-primary.large,
.btn-secondary.large {
    padding: 16px 32px;
    font-size: 16px;
}

/* 主要内容区域 */
main {
    margin-top: 70px;
}

/* 首页横幅 */
.hero-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fffe 0%, #f0fdfb 100%);
    min-height: 600px;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 24px;
    color: #1a1a1a;
}

.highlight {
    color: #06a17e;
    display: block;
}

.hero-description {
    font-size: 18px;
    color: #666;
    margin-bottom: 32px;
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.hero-image {
    text-align: center;
}

.hero-img {
    max-width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* 章节通用样式 */
.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 16px;
    color: #1a1a1a;
}

.section-subtitle {
    font-size: 18px;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* 解决方案部分 */
.solutions-section {
    padding: 100px 0;
    background: #fff;
}

.solutions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 32px;
}

.solution-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.solution-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-image {
    height: 200px;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.solution-card:hover .card-image img {
    transform: scale(1.05);
}

.card-content {
    padding: 32px;
}

.card-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #1a1a1a;
}

.card-description {
    color: #666;
    margin-bottom: 24px;
    line-height: 1.6;
}

.card-link {
    color: #06a17e;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.card-link:hover {
    color: #058a6e;
}

/* 产品特色部分 */
.features-section {
    padding: 100px 0;
    background: #f8fffe;
}

.features-content {
    display: grid;
    gap: 80px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
}

.feature-item {
    text-align: center;
    padding: 32px 24px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #06a17e, #04d9a5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    color: white;
}

.feature-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #1a1a1a;
}

.feature-description {
    color: #666;
    line-height: 1.6;
}

.features-showcase {
    margin-top: 40px;
}

.showcase-images {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.showcase-img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.showcase-img:hover {
    transform: scale(1.02);
}

/* 产品展示画廊 */
.gallery-section {
    padding: 100px 0;
    background: #fff;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;
    margin-top: 40px;
}

.gallery-item {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.gallery-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
}

.gallery-img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 32px 24px 24px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    transform: translateY(0);
}

.gallery-overlay h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
}

.gallery-overlay p {
    font-size: 14px;
    opacity: 0.9;
}

/* 联系我们部分 */
.contact-section {
    padding: 100px 0;
    background: #1a1a1a;
    color: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.contact-title {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 24px;
}

.contact-description {
    font-size: 18px;
    color: #ccc;
    margin-bottom: 32px;
    line-height: 1.7;
}

.contact-details {
    margin-bottom: 32px;
}

.contact-item {
    margin-bottom: 16px;
    font-size: 16px;
    color: #ccc;
}

.contact-item strong {
    color: white;
}

.contact-img {
    max-width: 100%;
    height: auto;
    border-radius: 12px;
}

/* 页脚 */
.footer {
    background: #111;
    color: white;
    padding: 40px 0;
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.footer-logo-img {
    height: 32px;
    width: auto;
    filter: brightness(0) invert(1);
}

.footer-logo-text {
    font-size: 18px;
    font-weight: 600;
}

.footer-text {
    color: #999;
}

/* 产品概述样式 */
.product-overview-section {
    padding: 60px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-number {
    display: inline-block;
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    letter-spacing: 2px;
    margin-bottom: 8px;
}

.section-title {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 6px;
    letter-spacing: -0.5px;
}

.section-subtitle {
    font-size: 14px;
    color: #64748b;
    font-weight: 400;
}

/* 企业痛点问题样式 */
.pain-points {
    margin-bottom: 50px;
}

.problems-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.problem-card {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.06);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
}

.problem-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.problem-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
}

.problem-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.problem-card.primary .problem-icon {
    color: #6b7280;
    background: #f3f4f6;
}

.problem-card.secondary .problem-icon {
    color: #6b7280;
    background: #f3f4f6;
}

.problem-card.tertiary .problem-icon {
    color: #6b7280;
    background: #f3f4f6;
}

.problem-card.quaternary .problem-icon {
    color: #6b7280;
    background: #f3f4f6;
}

.problem-card:hover .problem-icon {
    transform: scale(1.05);
}

.problem-card.primary:hover .problem-icon {
    color: #059669;
    background: rgba(16, 185, 129, 0.1);
}

.problem-card.secondary:hover .problem-icon {
    color: #1d4ed8;
    background: rgba(59, 130, 246, 0.1);
}

.problem-card.tertiary:hover .problem-icon {
    color: #d97706;
    background: rgba(245, 158, 11, 0.1);
}

.problem-card.quaternary:hover .problem-icon {
    color: #dc2626;
    background: rgba(239, 68, 68, 0.1);
}

.problem-card h4 {
    font-size: 15px;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
    line-height: 1.4;
    flex: 1;
}

.problem-card p {
    font-size: 13px;
    line-height: 1.6;
    color: #64748b;
    margin: 0;
}

/* 核心价值观样式 */
.core-values {
    margin-bottom: 30px;
}

.values-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.value-item {
    background: white;
    border-radius: 16px;
    padding: 30px 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.value-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.value-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 20px;
    background: #f8fafc;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.value-item:nth-child(1) .value-icon {
    color: #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.value-item:nth-child(2) .value-icon {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.value-item:nth-child(3) .value-icon {
    color: #8b5cf6;
    background: rgba(139, 92, 246, 0.1);
}

.value-item:nth-child(4) .value-icon {
    color: #f59e0b;
    background: rgba(245, 158, 11, 0.1);
}

.value-item:hover .value-icon {
    transform: scale(1.1);
}

.value-badge {
    display: inline-block;
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
}

.value-badge.green {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.value-badge.blue {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.value-badge.purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.value-badge.orange {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.value-item p {
    font-size: 14px;
    line-height: 1.6;
    color: #475569;
    margin: 0;
}

/* 产品定位样式 */
.product-positioning {
    max-width: 1200px;
    margin: 0 auto;
}

.subsection-header {
    text-align: center;
    margin-bottom: 20px;
}

.subsection-title {
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

.positioning-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.positioning-main {
    text-align: center;
    background: white;
    padding: 20px 24px;
    border-radius: 12px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.06);
    border: 1px solid #e2e8f0;
}

.positioning-badge {
    display: inline-block;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
}

.positioning-main p {
    font-size: 14px;
    line-height: 1.5;
    color: #475569;
    max-width: 500px;
    margin: 0 auto;
}

.positioning-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 20px;
}

.positioning-card {
    background: white;
    padding: 20px 16px;
    border-radius: 12px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.06);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    text-align: center;
}

.positioning-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.card-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 12px;
    background: #f8fafc;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.positioning-card:nth-child(1) .card-icon {
    color: #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.positioning-card:nth-child(2) .card-icon {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.positioning-card:nth-child(3) .card-icon {
    color: #8b5cf6;
    background: rgba(139, 92, 246, 0.1);
}

.positioning-card:nth-child(4) .card-icon {
    color: #f59e0b;
    background: rgba(245, 158, 11, 0.1);
}

.positioning-card:hover .card-icon {
    transform: scale(1.1);
}

.card-badge {
    display: inline-block;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    margin-bottom: 10px;
}

.card-badge.green {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.card-badge.blue {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.card-badge.purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.positioning-card p {
    font-size: 12px;
    line-height: 1.5;
    color: #475569;
    margin: 0;
}

.metrics-compact {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.metrics-compact span {
    font-size: 11px;
    color: #475569;
    padding: 4px 8px;
    background: #f1f5f9;
    border-radius: 6px;
    font-weight: 500;
    text-align: center;
}

/* 管理名言样式 */
.management-quote {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 24px;
    border-radius: 12px;
    text-align: center;
    border: 1px solid #e2e8f0;
    position: relative;
}

.quote-icon {
    width: 40px;
    height: 40px;
    margin: 0 auto 16px;
    background: rgba(100, 116, 139, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
}

.management-quote blockquote {
    font-size: 16px;
    font-style: italic;
    color: #334155;
    margin: 0 0 12px 0;
    font-weight: 500;
    line-height: 1.5;
}

.management-quote cite {
    font-size: 13px;
    color: #64748b;
    font-weight: 500;
    font-style: normal;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }
    
    .hero-title {
        font-size: 36px;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .hero-buttons {
        justify-content: center;
    }

    /* 产品概述移动端样式 */
    .product-overview-section {
        padding: 50px 0;
    }

    .section-title {
        font-size: 24px;
    }

    /* 痛点问题移动端样式 */
    .problems-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .problem-card {
        padding: 20px;
    }

    .problem-header {
        gap: 10px;
        margin-bottom: 14px;
    }

    .problem-icon {
        width: 36px;
        height: 36px;
    }

    .problem-card h4 {
        font-size: 14px;
    }

    .problem-card p {
        font-size: 12px;
    }

    .values-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .value-item {
        padding: 24px 20px;
    }

    .value-icon {
        width: 56px;
        height: 56px;
        margin-bottom: 16px;
    }

    .positioning-main {
        padding: 18px 20px;
    }

    .positioning-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .positioning-card {
        padding: 18px 16px;
    }

    .card-icon {
        width: 40px;
        height: 40px;
        margin-bottom: 12px;
    }

    .management-quote {
        padding: 20px;
    }

    .management-quote blockquote {
        font-size: 14px;
    }

    .core-values {
        margin-bottom: 60px;
    }
}
